<template>
  <el-drawer
    class="detail-drawer"
    :title="$T('详情')"
    :visible.sync="drawerVisible"
    destroy-on-close
    size="960px"
  >
    <div class="drawer-content">
      <!-- 基础信息 -->

      <el-row :gutter="24">
        <el-col :span="12">
          <div class="info-item">
            <div class="label">{{ $T("机组名称") }}</div>
            <div>{{ detailData.unitName || "--" }}</div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="info-item">
            <div class="label">{{ $T("机组类型") }}</div>
            <div>{{ detailData.unitType || "--" }}</div>
          </div>
        </el-col>
      </el-row>

      <!-- 资源列表 -->
      <div class="resource-list-section">
        <div class="section-title">
          {{ detailData.unitType + $T("资源列表") }}
        </div>

        <!-- 资源表格 -->
        <div class="resource-table-wrapper">
          <el-table :data="tableData" class="resource-table" height="440">
            <el-table-column
              type="index"
              :label="$T('序号')"
              width="60"
            ></el-table-column>

            <el-table-column
              prop="resourceId"
              :label="$T('资源ID')"
              min-width="80"
              show-overflow-tooltip
            />

            <el-table-column
              prop="resourceName"
              :label="$T('资源名称')"
              min-width="120"
              show-overflow-tooltip
            />

            <el-table-column
              prop="address"
              :label="$T('地址')"
              min-width="80"
              show-overflow-tooltip
            />
            <el-table-column
              prop="districtName"
              :label="$T('区域')"
              min-width="80"
              show-overflow-tooltip
            />

            <el-table-column
              prop="capacity"
              :label="$T('报装容量（kVA）')"
              min-width="120"
            />

            <el-table-column
              prop="directControl"
              :label="$T('平台直控')"
              min-width="80"
            >
              <template slot-scope="scope">
                {{ scope.row.directControl ? $T("是") : $T("否") }}
              </template>
            </el-table-column>

            <el-table-column :label="$T('操作')" width="80">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  class="unbind-btn"
                  @click="handleUnbind(scope.row)"
                >
                  {{ $T("解绑") }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 分页器 -->
        <div class="pagination-wrapper">
          <span>
            {{ $T("共") }}
            <span class="total-number">{{ totalResourceCount }}</span>
            {{ $T("个") }}
          </span>

          <el-pagination
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            :total="totalResourceCount"
            layout="sizes,prev, pager, next, jumper"
            @current-change="handlePageChange"
            @size-change="handlePageSizeChange"
          />
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import { unbindResource, getUnitResources } from "@/api/resource-aggregation";
import { getGeographicalData } from "@/api/base-config";
import { getGeographicalNameByCode } from "@/utils/geographicalUtils";

export default {
  name: "DetailDrawer",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detailData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 分页相关
      currentPage: 1,
      pageSize: 10,
      // 资源数据
      tableData: [],
      totalResourceCount: 0,
      // 区域数据
      areaOptions: []
    };
  },
  computed: {
    drawerVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.currentPage = 1; // 重置分页
        this.loadResourceData();
      }
    }
  },
  methods: {
    // 加载资源数据
    async loadResourceData() {
      if (!this.detailData || !this.detailData.unitId) {
        this.tableData = [];
        this.totalResourceCount = 0;
        return;
      }

      try {
        const params = {
          pageNum: this.currentPage,
          pageSize: this.pageSize,
          unitId: this.detailData.unitId
        };

        const response = await getUnitResources(params);

        if (response && response.data) {
          // 适配返回的数据格式
          const resourceList = response.data.records || [];
          this.tableData = resourceList.map(item => ({
            resourceId: item.id,
            resourceName: item.resource_name,
            district: item.district,
            districtName: getGeographicalNameByCode(
              this.areaOptions,
              item.province,
              item.city,
              item.district
            ),
            capacity: item.registered_capacity,
            directControl: item.platform_direct_control,
            address: item.address
          }));

          this.totalResourceCount = response.data?.total || 0;
        } else {
          this.tableData = [];
          this.totalResourceCount = 0;
        }
      } catch (error) {
        this.$message.error(this.$T("加载资源列表失败"));
        this.tableData = [];
        this.totalResourceCount = 0;
      }
    },
    // 加载区域列表
    async loadDistricts() {
      try {
        const response = await getGeographicalData();
        if (response && response.data) {
          this.areaOptions = response?.data || [];
        }
      } catch (error) {
        this.$message.error(this.$T("加载区域列表失败"));
      }
    },

    // 解绑操作
    handleUnbind(row) {
      this.$confirm(this.$T("确定要解绑该资源吗？"), this.$T("提示"), {
        confirmButtonText: this.$T("确定"),
        cancelButtonText: this.$T("取消"),
        type: "warning"
      })
        .then(async () => {
          try {
            await unbindResource(this.detailData.unitId, row.resourceId);
            this.$message.success(this.$T("解绑成功"));

            // 重新加载当前页数据
            await this.loadResourceData();

            // 如果当前页没有数据了，回到上一页
            if (this.tableData.length === 0 && this.currentPage > 1) {
              this.currentPage--;
              await this.loadResourceData();
            }

            // 通知父组件刷新列表
            this.$emit("resource-unbound");
          } catch (error) {
            this.$message.error(this.$T("解绑资源失败"));
          }
        })
        .catch(() => {
          // 取消操作
        });
    },

    // 分页变化
    handlePageChange(page) {
      this.currentPage = page;
      this.loadResourceData();
    },

    // 页面大小变化
    handlePageSizeChange(size) {
      this.pageSize = size;
      this.currentPage = 1; // 重置到第一页
      this.loadResourceData();
    }
  },
  mounted() {
    this.loadDistricts();
  }
};
</script>

<style lang="scss" scoped>
.detail-drawer {
  .drawer-content {
    @include padding(J4);
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--J4);

    .info-item {
      .label {
        @include font_color(T4);
      }
    }

    .resource-list-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: var(--J3);

      .section-title {
        font-weight: 700;
      }

      .resource-table-wrapper {
        @include background_color(BG1);
        border-radius: var(--Ra);
        overflow: hidden;

        .resource-table {
          width: 100%;
          table-layout: fixed;

          .unbind-btn {
            @include font_color(ZS);
            padding: 0;
          }

          // 确保表格单元格内容不会溢出
          :deep(.el-table__body-wrapper) {
            overflow-x: hidden;
          }

          // 处理长文本的显示
          :deep(.cell) {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }

      .pagination-wrapper {
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: var(--J3);
        .total-number {
          @include font_color(ZS);
        }
        .page-size-select {
          width: 100px;
        }
      }
    }
  }
}
</style>
