<template>
  <div class="test-tree-page">
    <h2>VppTree 组件测试页面</h2>
    <div class="description">
      <h3>层级关系说明：</h3>
      <p>虚拟电厂 → 用户 → 资源 → 站点 → 设备</p>
      <p>这是一个线性的层级结构，每个层级都是上一层级的子节点</p>
    </div>
    <div class="content-wrapper">
      <div class="tree-demo">
        <VppTree
          @node-click="handleNodeClick"
          @nodes-checked="handleNodesChecked"
        />
      </div>
      <div class="info-panel">
        <h3>选中节点信息：</h3>
        <pre>{{ selectedNodeInfo }}</pre>
      </div>
    </div>
  </div>
</template>

<script>
import VppTree from "./components/VppTree.vue";

export default {
  name: "TestTree",
  components: {
    VppTree
  },
  data() {
    return {
      selectedNodeInfo: null
    };
  },
  methods: {
    handleNodeClick(node) {
      console.log("选中节点:", node);
      this.selectedNodeInfo = {
        id: node.tree_id,
        name: node.name,
        type: node.type,
        isParent: node.isParent
      };
    },
    handleNodesChecked(nodes) {
      console.log("勾选节点:", nodes);
    }
  }
};
</script>

<style lang="scss" scoped>
.test-tree-page {
  padding: 20px;

  h2 {
    color: #424e5f;
    margin-bottom: 16px;
  }

  .description {
    background: #f6f8fa;
    padding: 16px;
    border-radius: 4px;
    margin-bottom: 20px;
    border-left: 4px solid #00b45e;

    h3 {
      margin-top: 0;
      margin-bottom: 8px;
      color: #424e5f;
      font-size: 16px;
    }

    p {
      margin: 4px 0;
      color: #798492;
      font-size: 14px;
    }
  }

  .content-wrapper {
    display: flex;
    gap: 20px;

    .tree-demo {
      flex-shrink: 0;
    }

    .info-panel {
      flex: 1;
      background: #f5f5f5;
      padding: 16px;
      border-radius: 4px;

      h3 {
        margin-top: 0;
        color: #424e5f;
      }

      pre {
        background: #ffffff;
        padding: 12px;
        border-radius: 4px;
        border: 1px solid #dcdfe6;
        font-size: 12px;
        color: #424e5f;
      }
    }
  }
}
</style>
