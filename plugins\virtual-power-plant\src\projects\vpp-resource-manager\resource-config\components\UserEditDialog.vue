<template>
  <el-dialog
    :title="$T('编辑用户')"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="onClose"
  >
    <div class="dialog-content">
      <el-form
        ref="editForm"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="left"
      >
        <!-- 只读信息 -->
        <div class="readonly-section mb-J4">
          <div class="section-title text-H4 font-bold mb-J3 text-T1">
            {{ $T("基本信息") }}
          </div>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="$T('用户名称')">
                <div class="readonly-value text-T1">
                  {{ formData.name || "--" }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('区域')">
                <div class="readonly-value text-T1">
                  {{ formData.area || "--" }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('资源数量')">
                <div class="readonly-value text-T1">
                  {{ formData.count || 0 }}
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 可编辑信息 -->
        <div class="editable-section">
          <div class="section-title text-H4 font-bold mb-J3 text-T1">
            {{ $T("联系信息") }}
          </div>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item :label="$T('联系人')" prop="contact">
                <el-input
                  v-model="formData.contact"
                  :placeholder="$T('请输入联系人')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="$T('联系电话')" prop="phone">
                <el-input
                  v-model="formData.phone"
                  :placeholder="$T('请输入联系电话')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item :label="$T('地址')" prop="address">
                <el-input
                  v-model="formData.address"
                  :placeholder="$T('请输入地址')"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="onCancel">{{ $T("取消") }}</el-button>
      <el-button type="primary" @click="onSave" :loading="saving">
        {{ $T("保存") }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "UserEditDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    userData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      saving: false,
      formData: {
        id: null,
        name: "",
        area: "",
        count: 0,
        contact: "",
        phone: "",
        address: ""
      },
      formRules: {
        contact: [
          { required: true, message: this.$T("请输入联系人"), trigger: "blur" },
          {
            min: 1,
            max: 50,
            message: this.$T("联系人长度在 1 到 50 个字符"),
            trigger: "blur"
          }
        ],
        phone: [
          {
            required: true,
            message: this.$T("请输入联系电话"),
            trigger: "blur"
          },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: this.$T("请输入正确的手机号码"),
            trigger: "blur"
          }
        ],
        address: [
          { required: true, message: this.$T("请输入地址"), trigger: "blur" },
          {
            min: 1,
            max: 200,
            message: this.$T("地址长度在 1 到 200 个字符"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
      if (val) {
        this.loadUserData();
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit("close");
      }
    }
  },
  methods: {
    loadUserData() {
      console.log("UserEditDialog - Loading user data:", this.userData);

      // 使用从API获取的真实用户数据
      this.formData = {
        id: this.userData.id,
        name: this.userData.name || "",
        area: this.userData.area || "",
        count: this.userData.count || 0,
        // 使用API返回的真实数据，支持多种字段名格式
        contact: this.userData.contact || this.userData.contactPerson || "",
        phone: this.userData.phone || this.userData.phoneNumber || "",
        address: this.userData.address || ""
      };

      console.log("UserEditDialog - Form data loaded:", this.formData);
    },
    onSave() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          console.log("UserEditDialog - Saving user data:", this.formData);

          // 发送保存事件，传递编辑后的数据给父组件处理API调用
          this.$emit("save", {
            id: this.formData.id,
            name: this.formData.name,
            contact: this.formData.contact,
            contactPerson: this.formData.contact, // 兼容字段名
            phone: this.formData.phone,
            phoneNumber: this.formData.phone, // 兼容字段名
            address: this.formData.address
          });
        } else {
          this.$message.error(this.$T("请检查输入信息"));
        }
      });
    },
    onCancel() {
      this.onClose();
    },
    onClose() {
      this.dialogVisible = false;
      this.$refs.editForm?.resetFields();
    }
  }
};
</script>

<style scoped>
.dialog-content {
  padding: var(--J2);
}

.readonly-section {
  background: var(--BG2);
  border-radius: var(--Ra1);
  padding: var(--J3);
  border: 1px solid var(--B2);
}

.editable-section {
  background: var(--BG1);
  border-radius: var(--Ra1);
  padding: var(--J3);
  border: 1px solid var(--B2);
}

.section-title {
  position: relative;
  padding-left: var(--J2);
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: var(--ZS);
  border-radius: 2px;
}

.readonly-value {
  font-size: var(--Aa);
  line-height: 32px;
  color: var(--T1);
}

.dialog-footer {
  text-align: right;
}

:deep(.el-form-item__label) {
  color: var(--T2);
  font-weight: normal;
}

:deep(.el-input__inner) {
  border-color: var(--B2);
}

:deep(.el-input__inner:focus) {
  border-color: var(--ZS);
}
</style>
