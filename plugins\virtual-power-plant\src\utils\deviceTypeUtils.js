/**
 * 设备类型工具函数
 * 用于统一处理设备类型的显示和转换
 */

/**
 * 管网设备类型映射表
 * 将英文的modelLabel转换为中文显示名称
 */
export const MODEL_LABEL_MAP = {
  // 电池和储能相关
  battery: "电池设备",
  pcs: "PCS设备",
  energy_container: "储能容器",
  pv_energycontainer: "储能容器",

  // 逆变器相关
  inverter: "逆变器",
  pv_inverter: "光伏逆变器",

  // 汇流箱相关
  combiner_box: "汇流箱",
  pv_dccombinerbox: "DC汇流箱",
  dc_combiner_box: "DC汇流箱",

  // 气象和监测设备
  meteorograph: "气象仪",
  pv_meteorograph: "气象仪",
  sensor: "传感器",
  controller: "控制器",

  // 充电设备
  charging_station: "充电桩",
  pv_chargingstation: "充电桩",

  // 泵类设备
  pump: "水泵",
  blower: "鼓风机",
  blowers: "鼓风机",

  // 空调设备
  fan_coil: "风机盘管",
  heat_pump: "热泵机组",
  heat_pump_units: "热泵机组",

  // 电力设备
  transformer: "变压器",
  switch: "开关设备",
  meter: "电表",
  ups: "UPS设备",

  // 用能设备
  manuequipment: "用能设备",

  // 其他常见设备类型
  wind_turbine: "风力发电机",
  solar_panel: "太阳能板",
  air_conditioner: "空调设备",
  lighting: "照明设备",
  elevator: "电梯设备"
};

/**
 * 设备model字段映射表
 * 用于根据具体的model字段推断设备类型
 */
export const MODEL_FIELD_MAP = [
  // 电池和储能相关
  { field: "pcs_model", type: "PCS设备" },
  { field: "pv_energycontainer_model", type: "储能容器" },
  { field: "battery_model", type: "电池设备" },

  // 逆变器相关
  { field: "pv_inverter_model", type: "光伏逆变器" },
  { field: "inverter_model", type: "逆变器" },

  // 汇流箱相关
  { field: "pv_dccombinerbox_model", type: "DC汇流箱" },
  { field: "combiner_box_model", type: "汇流箱" },

  // 气象和监测设备
  { field: "pv_meteorograph_model", type: "气象仪" },
  { field: "meteorograph_model", type: "气象仪" },
  { field: "sensor_model", type: "传感器" },
  { field: "controller_model", type: "控制器" },

  // 充电设备
  { field: "pv_chargingstation_model", type: "充电桩" },
  { field: "charging_station_model", type: "充电桩" },

  // 泵类设备
  { field: "pump_model", type: "水泵" },
  { field: "blowers_model", type: "鼓风机" },
  { field: "blower_model", type: "鼓风机" },

  // 空调设备
  { field: "fan_coil_model", type: "风机盘管" },
  { field: "heat_pump_units_model", type: "热泵机组" },
  { field: "heat_pump_model", type: "热泵机组" },
  { field: "air_conditioner_model", type: "空调设备" },

  // 电力设备
  { field: "transformer_model", type: "变压器" },
  { field: "switch_model", type: "开关设备" },
  { field: "meter_model", type: "电表" },
  { field: "ups_model", type: "UPS设备" },

  // 用能设备
  { field: "manuequipment_model", type: "用能设备" },

  // 其他设备
  { field: "wind_turbine_model", type: "风力发电机" },
  { field: "solar_panel_model", type: "太阳能板" },
  { field: "lighting_model", type: "照明设备" },
  { field: "elevator_model", type: "电梯设备" }
];

/**
 * 根据设备的modelLabel和model字段推断设备类型
 * @param {Object} device - 设备对象
 * @param {string} device.modelLabel - 管网设备类型标识
 * @param {string} device.name - 设备名称
 * @returns {string} 设备类型的中文显示名称
 */
export function getDeviceTypeFromModel(device) {
  // 优先使用modelLabel字段，这是新API返回的管网设备类型
  if (device.modelLabel) {
    return MODEL_LABEL_MAP[device.modelLabel] || device.modelLabel;
  }

  // 如果没有modelLabel，则根据API返回的数据结构，检查model字段
  for (const modelField of MODEL_FIELD_MAP) {
    if (device[modelField.field] && device[modelField.field] !== null) {
      return modelField.type;
    }
  }

  // 如果都没有找到，返回默认值
  return "未知设备";
}

/**
 * 获取设备类型的英文标识
 * @param {string} deviceTypeName - 设备类型中文名称
 * @returns {string} 设备类型的英文标识
 */
export function getDeviceTypeKey(deviceTypeName) {
  const entry = Object.entries(MODEL_LABEL_MAP).find(
    ([key, value]) => value === deviceTypeName
  );
  return entry ? entry[0] : deviceTypeName;
}

/**
 * 检查设备类型是否有效
 * @param {string} deviceType - 设备类型
 * @returns {boolean} 是否为有效的设备类型
 */
export function isValidDeviceType(deviceType) {
  return (
    Object.values(MODEL_LABEL_MAP).includes(deviceType) ||
    deviceType === "未知设备"
  );
}

/**
 * 获取所有支持的设备类型列表
 * @returns {Array} 设备类型列表
 */
export function getAllDeviceTypes() {
  return Object.values(MODEL_LABEL_MAP);
}

/**
 * 根据设备数据处理并标准化设备信息
 * @param {Object} device - 原始设备数据
 * @param {string} modelLabel - 管网设备类型（可选，用于分组数据）
 * @returns {Object} 处理后的设备数据
 */
export function processDeviceData(device, modelLabel = null) {
  return {
    ...device,
    modelLabel: device.modelLabel || modelLabel, // 确保有modelLabel字段
    deviceName: device.name || device.deviceName, // 统一字段名
    deviceType: getDeviceTypeFromModel(device) // 推断设备类型
  };
}
