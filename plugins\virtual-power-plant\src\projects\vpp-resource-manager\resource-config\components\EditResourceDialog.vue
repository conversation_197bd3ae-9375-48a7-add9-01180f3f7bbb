<template>
  <el-dialog
    :title="$T('编辑资源')"
    :visible.sync="dialogVisible"
    width="1000px"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :top="'5vh'"
    class="edit-resource-dialog"
    @close="onClose"
  >
    <div class="dialog-content">
      <el-form
        ref="editResourceForm"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="top"
      >
        <!-- 只读信息区域 -->
        <div class="readonly-section mb-J4">
          <div class="section-title text-H4 font-bold mb-J3 text-T1">
            {{ $T("基本信息") }}
          </div>
          <el-row :gutter="20">
            <!-- 电户号 -->
            <el-col :span="8">
              <el-form-item>
                <span slot="label">{{ $T("电户号") }}</span>
                <div class="readonly-value text-T1">
                  {{ formData.electricityUserNumbers || "--" }}
                </div>
              </el-form-item>
            </el-col>
            <!-- 平台直控 -->
            <el-col :span="8">
              <el-form-item>
                <span slot="label">{{ $T("平台直控") }}</span>
                <div class="readonly-value text-T1">
                  {{
                    getPlatformDirectControlLabel(
                      formData.platformDirectControl
                    )
                  }}
                </div>
              </el-form-item>
            </el-col>
            <!-- 资源类型 -->
            <el-col :span="8">
              <el-form-item>
                <span slot="label">{{ $T("资源类型") }}</span>
                <div class="readonly-value text-T1">
                  {{ getResourceTypeLabel(formData.resourceType) }}
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- 响应方式 -->
            <el-col :span="8">
              <el-form-item>
                <span slot="label">{{ $T("响应方式") }}</span>
                <div class="readonly-value text-T1">
                  {{ getResponseModeLabel(formData.responseMode) }}
                </div>
              </el-form-item>
            </el-col>

            <!-- 区域 -->
            <el-col :span="8">
              <el-form-item>
                <span slot="label">{{ $T("区域") }}</span>
                <div class="readonly-value text-T1">
                  {{
                    getRegionText(
                      formData.province,
                      formData.city,
                      formData.district
                    )
                  }}
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 可编辑信息区域 -->
        <div class="editable-section">
          <div class="section-title text-H4 font-bold mb-J3 text-T1">
            {{ $T("可编辑信息") }}
          </div>
          <el-row :gutter="20">
            <!-- 资源名称 -->
            <el-col :span="8">
              <el-form-item prop="resourceName">
                <span slot="label" class="required-label">
                  {{ $T("资源名称") }}
                </span>
                <el-input
                  v-model="formData.resourceName"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 报装容量 -->
            <el-col :span="8">
              <el-form-item prop="registeredCapacity">
                <span slot="label" class="required-label">
                  {{ $T("报装容量") }}
                </span>
                <el-input
                  v-model="formData.registeredCapacity"
                  :placeholder="$T('请输入数值')"
                  clearable
                >
                  <template slot="append">kVA</template>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 最大可运行功率 -->
            <el-col :span="8">
              <el-form-item prop="maxRunningPower">
                <span slot="label" class="required-label">
                  {{ $T("最大可运行功率") }}
                </span>
                <el-input
                  v-model="formData.maxRunningPower"
                  :placeholder="$T('请输入数值')"
                  clearable
                >
                  <template slot="append">kW</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- 最小可运行功率 -->
            <el-col :span="8">
              <el-form-item prop="minRunningPower">
                <span slot="label" class="required-label">
                  {{ $T("最小可运行功率") }}
                </span>
                <el-input
                  v-model="formData.minRunningPower"
                  :placeholder="$T('请输入数值')"
                  clearable
                >
                  <template slot="append">kW</template>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 最大上调速率 -->
            <el-col :span="8">
              <el-form-item prop="maxUpRate">
                <span slot="label">{{ $T("最大上调速率") }}</span>
                <el-input
                  v-model="formData.maxUpRate"
                  :placeholder="$T('请输入数值')"
                  clearable
                >
                  <template slot="append">kW/min</template>
                </el-input>
              </el-form-item>
            </el-col>
            <!-- 最大下调速率 -->
            <el-col :span="8">
              <el-form-item prop="maxDownRate">
                <span slot="label">{{ $T("最大下调速率") }}</span>
                <el-input
                  v-model="formData.maxDownRate"
                  :placeholder="$T('请输入数值')"
                  clearable
                >
                  <template slot="append">kW/min</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- 经度 -->
            <el-col :span="6">
              <el-form-item prop="longitude">
                <span slot="label">{{ $T("经度") }}</span>
                <el-input
                  v-model="formData.longitude"
                  :placeholder="$T('请输入经度')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 纬度 -->
            <el-col :span="6">
              <el-form-item prop="latitude">
                <span slot="label">{{ $T("纬度") }}</span>
                <el-input
                  v-model="formData.latitude"
                  :placeholder="$T('请输入纬度')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 联系人 -->
            <el-col :span="6">
              <el-form-item prop="contactPerson">
                <span slot="label">{{ $T("联系人") }}</span>
                <el-input
                  v-model="formData.contactPerson"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
            <!-- 联系电话 -->
            <el-col :span="6">
              <el-form-item prop="contactPhone">
                <span slot="label">{{ $T("联系电话") }}</span>
                <el-input
                  v-model="formData.contactPhone"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- 地址 -->
            <el-col :span="24">
              <el-form-item prop="address">
                <span slot="label">{{ $T("地址") }}</span>
                <el-input
                  v-model="formData.address"
                  :placeholder="$T('请输入内容')"
                  clearable
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onCancel">{{ $T("取消") }}</el-button>
      <el-button type="primary" @click="onSave" :loading="saving">
        {{ $T("保存") }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getEnumOptions } from "../../../../utils/enumManager";
import { getGeographicalData } from "@/api/base-config";

export default {
  name: "EditResourceDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    resourceData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(value) {
        if (!value) {
          this.$emit("close");
        }
      }
    },
    // 从 enumManager 获取资源类型选项
    resourceTypeOptions() {
      return getEnumOptions("RESOURCE_TYPE");
    },
    // 从 enumManager 获取响应方式选项
    responseModeOptions() {
      return getEnumOptions("RESPONSE_MODE");
    }
  },
  data() {
    return {
      saving: false,
      geographicalData: null, // 存储地理数据
      formData: {
        electricityUserNumbers: "",
        resourceName: "",
        registeredCapacity: "",
        platformDirectControl: "",
        resourceType: "",
        responseMode: "",
        maxRunningPower: "",
        minRunningPower: "",
        maxUpRate: "",
        maxDownRate: "",
        longitude: "",
        latitude: "",
        contactPerson: "",
        contactPhone: "",
        province: "",
        city: "",
        district: "",
        address: ""
      },
      formRules: {
        resourceName: [
          {
            required: true,
            message: this.$T("资源名称不能为空"),
            trigger: "blur"
          }
        ],
        registeredCapacity: [
          {
            required: true,
            message: this.$T("报装容量不能为空"),
            trigger: "blur"
          }
        ],
        maxRunningPower: [
          {
            required: true,
            message: this.$T("最大可运行功率不能为空"),
            trigger: "blur"
          }
        ],
        minRunningPower: [
          {
            required: true,
            message: this.$T("最小可运行功率不能为空"),
            trigger: "blur"
          }
        ]
      },
      // 选项数据
      platformDirectControlOptions: [
        { label: "是", value: true },
        { label: "否", value: false }
      ]
    };
  },
  async mounted() {
    // 加载地理数据
    await this.loadGeographicalData();
  },
  watch: {
    resourceData: {
      handler(newData) {
        if (newData && Object.keys(newData).length > 0) {
          this.loadResourceData();
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 加载地理数据
    async loadGeographicalData() {
      try {
        const response = await getGeographicalData();
        if (response.code === 0) {
          this.geographicalData = response.data;
        } else {
          console.error("加载地理数据失败:", response.msg);
        }
      } catch (error) {
        console.error("加载地理数据失败:", error);
      }
    },
    loadResourceData() {
      // 加载资源数据进行回显，处理字段映射（API返回驼峰命名）
      this.formData = {
        id: this.resourceData.id,
        electricityUserNumbers: this.resourceData.electricityUserNumbers || "",
        resourceName: this.resourceData.resourceName || "",
        registeredCapacity: this.resourceData.registeredCapacity || "",
        platformDirectControl: this.resourceData.platformDirectControl || false,
        resourceType: this.resourceData.resourceType || "",
        responseMode: this.resourceData.responseMode || "",
        maxRunningPower: this.resourceData.maximumOperablePower || "",
        minRunningPower: this.resourceData.minmumOperablePower || "",
        maxUpRate: this.resourceData.maximumUpscalingRate || "",
        maxDownRate: this.resourceData.maximumDownwardRate || "",
        longitude: this.resourceData.longitude || "",
        latitude: this.resourceData.latitude || "",
        contactPerson: this.resourceData.contactPerson || "",
        contactPhone: this.resourceData.phoneNumber || "",
        province: this.resourceData.province || "",
        city: this.resourceData.city || "",
        district: this.resourceData.district || "",
        address: this.resourceData.address || ""
      };
    },
    mapResourceType(type) {
      const typeMap = {
        load: "consumption",
        solar: "generation",
        storage: "storage",
        fault: "generation"
      };
      return typeMap[type] || "generation";
    },

    getRegionText(provinceId, cityId, districtId) {
      if (!this.geographicalData || !Array.isArray(this.geographicalData)) {
        return "--";
      }

      let regionText = "";

      // 查找省份名称
      if (provinceId) {
        const province = this.geographicalData.find(p => p.code === provinceId);
        if (province) {
          regionText = province.name;

          // 查找城市名称
          if (cityId && province.children && Array.isArray(province.children)) {
            const city = province.children.find(c => c.code === cityId);
            if (city && city.name !== province.name) {
              regionText += "/" + city.name;

              // 查找区县名称
              if (districtId && city.children && Array.isArray(city.children)) {
                const district = city.children.find(d => d.code === districtId);
                if (district && district.name !== city.name) {
                  regionText += "/" + district.name;
                }
              }
            }
          }
        }
      }

      return regionText || "--";
    },
    getPlatformDirectControlLabel(value) {
      const option = this.platformDirectControlOptions.find(
        item => item.value === value
      );
      return option ? this.$T(option.label) : "--";
    },
    getResourceTypeLabel(value) {
      const option = this.resourceTypeOptions.find(
        item => item.value === value
      );
      return option ? this.$T(option.label) : "--";
    },
    getResponseModeLabel(value) {
      const option = this.responseModeOptions.find(
        item => item.value === value
      );
      return option ? this.$T(option.label) : "--";
    },

    onSave() {
      this.$refs.editResourceForm.validate(valid => {
        if (valid) {
          this.saving = true;

          // 模拟保存操作
          setTimeout(() => {
            this.saving = false;
            this.$message.success(this.$T("保存成功"));

            // 发送保存事件，只传递可编辑的字段
            this.$emit("save", {
              id: this.formData.id,
              resourceName: this.formData.resourceName,
              registeredCapacity: this.formData.registeredCapacity,
              maxRunningPower: this.formData.maxRunningPower,
              minRunningPower: this.formData.minRunningPower,
              maxUpRate: this.formData.maxUpRate,
              maxDownRate: this.formData.maxDownRate,
              longitude: this.formData.longitude,
              latitude: this.formData.latitude,
              contactPerson: this.formData.contactPerson,
              contactPhone: this.formData.contactPhone,
              address: this.formData.address
            });

            this.onClose();
          }, 1000);
        } else {
          this.$message.error(this.$T("请检查输入信息"));
        }
      });
    },
    onCancel() {
      this.onClose();
    },
    onClose() {
      this.$refs.editResourceForm?.resetFields();
      this.$emit("close");
    }
  }
};
</script>

<style scoped>
.dialog-content {
  padding: var(--J2);
  max-height: calc(90vh - 160px); /* 确保内容区域不会超出视窗 */
  overflow-y: auto;
}

.readonly-section {
  background: var(--BG2);
  border-radius: var(--Ra1);
  padding: var(--J3);
  border: 1px solid var(--B2);
}

.editable-section {
  background: var(--BG1);
  border-radius: var(--Ra1);
  padding: var(--J3);
  border: 1px solid var(--B2);
}

.section-title {
  position: relative;
  padding-left: var(--J2);
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: var(--ZS);
  border-radius: 2px;
}

.readonly-value {
  font-size: var(--Aa);
  line-height: 32px;
  color: var(--T1);
}

.required-label::before {
  content: "*";
  color: var(--ER);
  margin-right: 4px;
}

.dialog-footer {
  text-align: right;
}

/* 弹窗样式 */
.edit-resource-dialog .el-dialog {
  margin-top: 5vh !important;
  margin-bottom: 5vh !important;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
}

.edit-resource-dialog .el-dialog__body {
  padding: 0;
  flex: 1;
  overflow-y: auto;
  max-height: calc(90vh - 120px); /* 减去头部和底部的高度 */
}

.edit-resource-dialog .el-form-item__label {
  color: var(--T1);
  font-weight: 500;
}

.edit-resource-dialog .el-input__inner {
  background: var(--BG1);
  border-color: var(--B2);
  color: var(--T1);
}

.edit-resource-dialog .el-input__inner:focus {
  border-color: var(--ZS);
}

.edit-resource-dialog .el-select .el-input__inner {
  background: var(--BG1);
}

/* 响应式调整 */
@media (max-height: 800px) {
  .edit-resource-dialog .el-dialog {
    margin-top: 2vh !important;
    margin-bottom: 2vh !important;
    max-height: 96vh;
  }

  .edit-resource-dialog .el-dialog__body {
    max-height: calc(96vh - 120px);
  }

  .dialog-content {
    max-height: calc(96vh - 160px);
  }
}

@media (max-height: 600px) {
  .edit-resource-dialog .el-dialog {
    margin-top: 1vh !important;
    margin-bottom: 1vh !important;
    max-height: 98vh;
  }

  .edit-resource-dialog .el-dialog__body {
    max-height: calc(98vh - 120px);
  }

  .dialog-content {
    max-height: calc(98vh - 160px);
  }
}
</style>
