# 地理位置数据使用指南

## 概述

本指南介绍如何在Vue组件中使用简化后的baseConfig store来获取和使用地理位置数据（省市区三级联动）。

## Store结构

### State
```javascript
{
  geographicalData: null  // 地理数据（省市区三级嵌套结构）
}
```

### 数据格式
```javascript
[
  {
    "children": [
      {
        "children": [
          {
            "children": null,
            "modelLabel": "district",
            "code": 110101,
            "name": "东城区"
          }
        ],
        "modelLabel": "city",
        "code": 110100,
        "name": "北京市"
      }
    ],
    "modelLabel": "province",
    "code": 110000,
    "name": "北京市"
  }
]
```

## 在组件中使用

### 1. 引入Vuex辅助函数

```javascript
import { mapActions, mapGetters } from "vuex";
```

### 2. 映射getters和actions

```javascript
export default {
  computed: {
    ...mapGetters("baseConfig", ["geographicalData"]),
    
    // 省份选项
    provinceOptions() {
      if (!this.geographicalData || !Array.isArray(this.geographicalData)) {
        return [];
      }
      return this.geographicalData.map(province => ({
        code: province.code,
        name: province.name
      }));
    }
  },
  
  methods: {
    ...mapActions("baseConfig", ["loadGeographicalData"]),
    
    // 初始化地理数据
    async initGeographicalData() {
      try {
        await this.loadGeographicalData();
      } catch (error) {
        console.error("加载地理数据失败:", error);
        this.$message.error("加载省份数据失败");
      }
    }
  }
}
```

### 3. 获取城市和区县数据

```javascript
computed: {
  // 根据选中的省份获取城市列表
  cityOptions() {
    if (!this.selectedProvinceCode) return [];
    
    const cities = this.$store.getters["baseConfig/getCitiesByProvinceCode"](this.selectedProvinceCode);
    return cities.map(city => ({
      code: city.code,
      name: city.name
    }));
  },
  
  // 根据选中的城市获取区县列表
  districtOptions() {
    if (!this.selectedCityCode) return [];
    
    const districts = this.$store.getters["baseConfig/getDistrictsByCityCode"](this.selectedCityCode);
    return districts.map(district => ({
      code: district.code,
      name: district.name
    }));
  }
}
```

### 4. 在模板中使用

```vue
<template>
  <!-- 省份选择 -->
  <el-select v-model="form.province" placeholder="请选择省份">
    <el-option
      v-for="province in provinceOptions"
      :key="province.code"
      :label="province.name"
      :value="province.code"
    ></el-option>
  </el-select>
  
  <!-- 城市选择 -->
  <el-select v-model="form.city" placeholder="请选择城市">
    <el-option
      v-for="city in cityOptions"
      :key="city.code"
      :label="city.name"
      :value="city.code"
    ></el-option>
  </el-select>
  
  <!-- 区县选择 -->
  <el-select v-model="form.district" placeholder="请选择区县">
    <el-option
      v-for="district in districtOptions"
      :key="district.code"
      :label="district.name"
      :value="district.code"
    ></el-option>
  </el-select>
</template>
```

## 完整示例

参考 `src/projects/vpp-resource-manager/virtual-power-plant-config/components/ConfigDialog.vue` 文件，该文件已经按照最佳实践进行了更新。

## 最佳实践

1. **数据加载时机**：在组件mounted时或对话框打开时加载数据
2. **错误处理**：使用try-catch处理加载失败的情况
3. **数据缓存**：store会自动缓存数据，避免重复请求
4. **响应式更新**：使用computed属性确保数据变化时UI自动更新

## 注意事项

1. 确保在使用前先调用`loadGeographicalData()`加载数据
2. 数据结构为三级嵌套，使用对应的getter方法获取子级数据
3. 所有的code都是数字类型，name是字符串类型
4. 如果数据未加载或加载失败，相关的computed属性会返回空数组
